// Represents an Email object with a message content.
class Email {
    private String message; // The content of the email

    // Constructor for the Email class (can be empty if message is set later)
    public Email() {
        this.message = ""; // Initialize with an empty message
    }

    // Method to set the message content of the email
    public void setMessage(String message) {
        this.message = message;
        System.out.println("Email: Message set to \"" + message + "\"");
    }

    // Method to get the message content of the email
    public String getMessage() {
        return this.message;
    }
}

// Represents a Human participating in the email exchange.
class Human {
    private String name; // The name of the human (e.g., <PERSON>, <PERSON>)
    private Email receivedEmail; // Stores the email received by this human

    // Constructor for the Human class, takes a name as an argument
    public Human(String name) {
        this.name = name;
        System.out.println(this.name + " has entered the simulation.");
    }

    // Method for the human to write a message to an email object.
    // It takes an Email object and the message string.
    public void wrote(Email mail, String content) {
        System.out.println(this.name + ": Is writing an email...");
        mail.setMessage(content); // Set the message in the provided Email object
        System.out.println(this.name + ": Finished writing the email.");
    }

    // Method for the human to send an email.
    // It returns the Email object that was "sent".
    public Email sent(Email mail) {
        System.out.println(this.name + ": Is sending the email...");
        System.out.println(this.name + ": Email sent!");
        return mail; // Return the email that was just written/sent
    }

    // Method for the human to receive an email.
    // It takes an Email object as an argument and stores it.
    public void receive(Email mail) {
        System.out.println(this.name + ": A new email has arrived!");
        this.receivedEmail = mail; // Store the received email
        System.out.println(this.name + ": Email received.");
    }

    // Method for the human to read the received email.
    public void read() {
        if (this.receivedEmail != null) {
            System.out.println(this.name + ": Is reading the email...");
            System.out.println("Email content for " + this.name + ": \"" + this.receivedEmail.getMessage() + "\"");
            System.out.println(this.name + ": 'Oh, that's nice! " + this.name + " is asking how I am today.'");
        } else {
            System.out.println(this.name + ": Has no email to read.");
        }
    }
}

// The main class to run the simulation
public class EmailSimulator {
    public static void main(String[] args) {
        System.out.println("--- Starting Email Simulation with Objects ---");

        // 1. Create Human objects for Hannah and Gina
        Human hannah = new Human("Hannah");
        Human gina = new Human("Gina");

        // 2. Create an Email object
        Email mail = new Email();

        System.out.println("\n--- Email Exchange Process ---");

        // 3. Hannah writes the email
        hannah.wrote(mail, "How are you Today?");

        // 4. Gina receives the email Hannah sent
        gina.receive(hannah.sent(mail)); // Hannah sends 'mail' and Gina receives it

        // 5. Gina reads the email
        gina.read();

        System.out.println("\n--- Email Simulation Complete ---");
    }
}
