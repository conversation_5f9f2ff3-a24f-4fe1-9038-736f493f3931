// Define a class to simulate the transaction
public class TransactionSimulator {

    // The main method where the program execution begins
    public static void main(String[] args) {

        // --- Transaction Setup ---
        // Declare and initialize variables for the transaction
        int appleCost = 20; // The price of one apple
        int paulsPocketMoney = 50; // The amount of money <PERSON> has

        System.out.println("--- Starting the Apple Purchase Simulation ---");
        System.out.println("Apple cost: $" + appleCost);
        System.out.println("<PERSON> has: $" + paulsPocketMoney + " in his pocket.\n");

        // --- <PERSON>'s Actions (Scanning and Receiving Payment) ---
        System.out.println("Rachel: 'Hello Paul! That apple looks delicious.'");
        System.out.println("<PERSON> scans the apple. The amount due is: $" + appleCost);
        System.out.println("<PERSON> gives <PERSON> $" + paulsPocketMoney + " for the apple.");
        System.out.println("<PERSON> receives the payment of $" + paulsPocketMoney + " from <PERSON>.\n");

        // --- <PERSON>'s Actions (Calculating and Giving Change) ---
        // Calculate the change Rachel needs to give back to <PERSON>
        int change = paulsPocketMoney - appleCost;

        System.out.println("<PERSON> calculates the change: $" + paulsPocketMoney + " - $" + appleCost + " = $" + change);
        System.out.println("Rachel: 'Here's your change, Paul!'");
        System.out.println("Rachel gives $" + change + " back to Paul.\n");

        // --- <PERSON>'s Actions (Receiving Change) ---
        System.out.println("Paul receives the change of $" + change + ".");
        System.out.println("Paul puts the $" + change + " back into his pocket.");

        // Calculate Paul's remaining money after the transaction
        int paulsRemainingMoney = change; // Since he paid exactly the amount he had and received change

        System.out.println("\n--- Transaction Complete ---");
        System.out.println("Paul now has: $" + paulsRemainingMoney + " in his pocket.");
    }
}